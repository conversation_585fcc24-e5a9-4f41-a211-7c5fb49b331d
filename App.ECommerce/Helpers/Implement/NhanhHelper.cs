using App.Base.Repository.Entities;
using App.Base.Utilities;
using App.ECommerce.Helpers.Interface;
using App.ECommerce.Repository.Entities;
using App.ECommerce.Repository.Interface;
using App.ECommerce.Resource.Dtos;
using App.ECommerce.Resource.Dtos.NhanhDtos;
using App.ECommerce.Resource.Dtos.Webhooks;
using App.ECommerce.Resource.Model;
using App.ECommerce.Services.UploadStore;
using App.ECommerce.Units.Abstractions.Entities;
using App.ECommerce.Units.Consts;
using App.ECommerce.Units.Enums;

using AutoMapper;

using MongoDB.Bson;

using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace App.ECommerce.Helpers;

public class NhanhHelper : INhanhHelper
{
    protected readonly ICryptoRepository _cryptoRepository;
    protected readonly ISyncServiceConfigRepository _syncConfigRepository;
    protected readonly ISyncServiceHelper _syncServiceHelper;
    protected readonly IMapper _mapper;
    protected readonly IItemsRepository _itemsRepository;
    protected readonly IOrderRepository _orderRepository;
    protected readonly ICategoryRepository _categoryRepository;
    protected readonly IShopRepository _shopRepository;
    protected readonly IUserRepository _userRepository;
    protected readonly IWarehouseRepository _warehouseRepository;

    public NhanhHelper(
        ICryptoRepository cryptoRepository,
        ISyncServiceConfigRepository syncConfigRepository,
        ISyncServiceHelper syncServiceHelper,
        IMapper mapper,
        IItemsRepository itemsRepository,
        IOrderRepository orderRepository,
        ICategoryRepository categoryRepository,
        IShopRepository shopRepository,
        IUserRepository userRepository,
        IWarehouseRepository warehouseRepository
    )
    {
        _cryptoRepository = cryptoRepository;
        _syncConfigRepository = syncConfigRepository;
        _syncServiceHelper = syncServiceHelper;
        _mapper = mapper;
        _itemsRepository = itemsRepository;
        _orderRepository = orderRepository;
        _categoryRepository = categoryRepository;
        _shopRepository = shopRepository;
        _userRepository = userRepository;
        _warehouseRepository = warehouseRepository;
    }

    public async Task<Result<SyncServiceConfig>> SaveNhanhConfig(SyncServiceConfigDto dto)
    {
        try
        {
            // Mã hóa SecretKey trước khi lưu vào database
            string encryptedSecretKey = _cryptoRepository.Encrypt(dto.SecretKey);

            var config = new SyncServiceConfig
            {
                ShopId = dto.ShopId,
                SyncService = SyncServiceEnum.NhanhVN,
                AppId = dto.AppId,
                SecretKey = encryptedSecretKey,
                Status = TypeStatus.InActived,
                AdditionalConfig = dto.AdditionalConfig
            };

            var savedConfig = await _syncConfigRepository.CreateOrUpdate(config);
            return Result<SyncServiceConfig>.Success(savedConfig);
        }
        catch (Exception ex)
        {
            return Result<SyncServiceConfig>.Failure("SYNC_NHANH_CONFIG_SAVE_ERROR");
        }
    }
    public async Task<SyncServiceConfig> GetNhanhConfig(string shopId)
    {
        var config = await _syncConfigRepository.FindByShopIdAndService(shopId, SyncServiceEnum.NhanhVN);
        return config;
    }

    public async Task<Result<bool>> DeleteNhanhConfig(string shopId)
    {
        try
        {
            var config = await GetNhanhConfig(shopId);
            if (config == null) return Result<bool>.Failure("SYNC_NHANH_CONFIG_NOT_FOUND");

            var deleted = await _syncConfigRepository.DeleteByShopIdAndService(shopId, SyncServiceEnum.NhanhVN);
            return Result<bool>.Success(true);
        }
        catch (Exception ex)
        {
            return Result<bool>.Failure("SYNC_NHANH_CONFIG_DELETE_ERROR");
        }
    }

    public async Task<Result<SyncServiceConfig>> UpdateNhanhAccessCode(string shopId, string accessCode)
    {
        var config = await GetNhanhConfig(shopId);
        if (config == null)
            return Result<SyncServiceConfig>.Failure("SYNC_NHANH_CONFIG_NOT_FOUND");
        config.SecretKey = _cryptoRepository.Decrypt(config.SecretKey);
        await _syncConfigRepository.UpdateAccessCode(shopId, SyncServiceEnum.NhanhVN, accessCode);

        var tokenResponse = await _syncConfigRepository.GetAccessTokenAsync(SyncServiceEnum.NhanhVN, config.AppId, accessCode, config.SecretKey) as NhanhAccessTokenResponseDto;
        if (!string.IsNullOrEmpty(tokenResponse?.AccessToken))
        {
            await _syncConfigRepository.UpdateAccessToken(shopId, SyncServiceEnum.NhanhVN, tokenResponse.AccessToken);
        }
        if (tokenResponse?.BusinessId != null)
        {
            await _syncConfigRepository.UpdateBusinessId(shopId, SyncServiceEnum.NhanhVN, tokenResponse.BusinessId);
        }
        if (string.IsNullOrEmpty(config.VerifyToken))
        {
            var verifyToken = TokenUtil.GenerateRandomVerifyToken();
            await _syncConfigRepository.UpdateVerifyToken(shopId, SyncServiceEnum.NhanhVN, verifyToken);
        }

        // Cập nhật trạng thái config thành actived sau khi hoàn thành tất cả các bước cập nhật
        await _syncConfigRepository.UpdateStatus(shopId, SyncServiceEnum.NhanhVN, TypeStatus.Actived);

        return Result<SyncServiceConfig>.Success(config);
    }

    public async Task<Result<bool>> SyncNhanhProductFromWebhook(NhanhProductWebhookDto productData, string shopId)
    {

        Shop? shop = _shopRepository.FindByShopId(shopId);
        if (shop == null) return Result<bool>.Failure("SHOP_NOT_FOUND");

        // Lấy categories từ Nhanh.vn
        var categories = await GetNhanhCategoriesAsync(shopId);
        var categoryObj = FindCategoryById(categories.Data, productData.CategoryId);
        if (categoryObj == null)
            return Result<bool>.Failure("SYNC_NHANH_CATEGORY_NOT_FOUND");

        // Tạo hoặc tìm category
        var category = await _syncServiceHelper.FindCategoryByExternalId(categoryObj.Id.ToString(), SyncServiceEnum.NhanhVN);
        if (category == null)
        {
            await CreateCategoryWithHierarchy(categoryObj, shop);
        }

        // Lấy tất cả category IDs bao gồm cả children nếu có
        var categoryIds = await GetAllCategoryIdsFromHierarchy(categoryObj, shop);

        // Ánh xạ dữ liệu
        var existingItem = _syncServiceHelper.FindItemByExternalId(productData.ProductId.ToString(), SyncServiceEnum.NhanhVN);

        var productDto = MapNhanhProductToProductDto(productData, shopId, categoryIds);

        if (existingItem != null)
        {
            // Update
            productDto.ItemsId = existingItem.ItemsId;
            _mapper.Map(productDto, existingItem);
            existingItem.Updated = DateTime.UtcNow;
            _itemsRepository.UpdateItems(existingItem);
        }
        else
        {
            // Add
            var items = _mapper.Map<Items>(productDto);
            items.ItemsId = Guid.NewGuid().ToString();
            items.Created = DateTime.Now;
            items.ExternalSource = SyncServiceEnum.NhanhVN;
            items.ExternalId = productData.ProductId.ToString();
            _itemsRepository.CreateItems(items);
        }

        return Result<bool>.Success(true);

    }

    public async Task<Result<bool>> SyncNhanhOrderFromWebhook(NhanhOrderDataDto orderData, string shopId)
    {
        var existingOrder = _syncServiceHelper.FindOrderByExternalId(orderData.OrderId.ToString(), SyncServiceEnum.NhanhVN);
        var nhanhCustomer = await GetNhanhCustomerByIdAsync(shopId, orderData.CustomerId);
        User user = EnsureUserFromNhanhCustomer(nhanhCustomer.Data, shopId);
        var listItems = new List<ItemsOrder>();
        foreach (var prod in orderData.Products ?? new List<NhanhOrderProductDto>())
        {
            var item = _syncServiceHelper.FindItemByExternalId(prod.Id.ToString(), SyncServiceEnum.NhanhVN);
            if (item != null)
            {
                ItemsOrder itemsOrder = _mapper.Map<ItemsOrder>(item);
                itemsOrder.Quantity = prod.Quantity;
                itemsOrder.TotalBeforeTax = (decimal)prod.Price;
                itemsOrder.TotalAfterTax = (decimal)prod.Price;
                listItems.Add(itemsOrder);
            }
        }

        var shippingAddress = new ShippingAddress
        {
            UserId = user.UserId,
            ProvinceName = orderData.CustomerCity,
            DistrictName = orderData.CustomerDistrict,
            WardName = orderData.CustomerWard,
            Address = orderData.CustomerAddress,
            FullName = orderData.CustomerName,
            PhoneNumber = orderData.CustomerMobile,
        };

        // Tính tổng giá từ các items
        var totalPrice = listItems.Sum(item => (item.Price ?? 0) * (item.Quantity ?? 0));

        if (existingOrder != null)
        {
            // Update existing order
            // existingOrder.ListItems = listItems;
            // existingOrder.UserShippingAddress = shippingAddress;
            // existingOrder.Creator = shippingAddress;
            existingOrder.StatusOrder = MapOrderStatusFromNhanhWebhook(orderData.Status);
            existingOrder.StatusTransport = MapTransportStatusFromNhanhWebhook(orderData.Status);
            // existingOrder.Price = totalPrice;
            // existingOrder.TransportPrice = (long)orderData.CustomerShipFee;
            // existingOrder.TotalAfterTax = (decimal)orderData.CalcTotalMoney;
            // existingOrder.Notes = orderData.Description;
            existingOrder.Updated = DateTime.UtcNow;
            _orderRepository.UpdateOrder(existingOrder);
        }
        else
        {
            // Thêm mới đơn hàng
            var order = new Order
            {
                OrderId = Guid.NewGuid().ToString(),
                ShopId = shopId,
                OrderNo = Id64.Generator(),
                ListItems = listItems,
                Price = totalPrice,
                Notes = orderData.Description,
                StatusDelivery = TypeDelivery.ExpressDelivery,
                OrderOrigin = TypeOrigin.Website,
                StatusOrder = MapOrderStatusFromNhanhWebhook(orderData.Status),
                StatusTransport = MapTransportStatusFromNhanhWebhook(orderData.Status),
                TransportPrice = (long)orderData.CustomerShipFee,
                TotalAfterTax = (decimal)orderData.CalcTotalMoney,
                UserShippingAddress = shippingAddress,
                Creator = shippingAddress,
                Created = DateTime.TryParse(orderData.CreatedDateTime, out var created) ? created : DateTime.Now,
                Status = TypeStatus.Actived,
                ExternalSource = SyncServiceEnum.NhanhVN,
                ExternalId = orderData.OrderId.ToString(),
            };
            await _orderRepository.CreateOrder(order);
        }

        return Result<bool>.Success(true);
    }
    private User EnsureUserFromNhanhCustomer(NhanhSyncCustomerDto nhanhCustomer, string shopId)
    {
        User? user = null;
        if (!string.IsNullOrEmpty(nhanhCustomer.Mobile))
            user = _userRepository.FindByUserPhone(shopId, nhanhCustomer.Mobile);

        if (user == null)
        {
            user = new User
            {
                UserId = Guid.NewGuid().ToString(),
                ShopId = shopId,
                Email = nhanhCustomer.Email,
                PhoneNumber = nhanhCustomer.Mobile,
                Fullname = nhanhCustomer.Name,
                Address = nhanhCustomer.Address,
                Status = TypeStatus.Actived,
                Created = DateTime.Now,
            };
            _userRepository.CreateUser(user);
        }
        else
        {
            user.Email = nhanhCustomer.Email;
            user.Fullname = nhanhCustomer.Name;
            user.PhoneNumber = nhanhCustomer.Mobile;
            user.Address = nhanhCustomer.Address;
            user.Updated = DateTime.Now;
            _userRepository.UpdateUser(user);
        }
        return user;
    }
    private async Task<Result<NhanhSyncCustomerDto>> GetNhanhCustomerByIdAsync(string shopId, int customerId)
    {
        var config = await _syncConfigRepository.FindByShopIdAndService(shopId, SyncServiceEnum.NhanhVN);
        if (config == null || string.IsNullOrEmpty(config.AccessToken))
            return Result<NhanhSyncCustomerDto>.Failure("SYNC_NHANH_CONFIG_NOT_FOUND");

        using var httpClient = new HttpClient();
        var urlCategory = NhanhConstants.ApiCustomer;

        var form = new MultipartFormDataContent
        {
            { new StringContent("2.0"), "version" },
            { new StringContent(config.AppId), "appId" },
            { new StringContent(config.BusinessId.ToString()), "businessId" },
            { new StringContent(config.AccessToken), "accessToken" },
            { new StringContent(JsonConvert.SerializeObject(new { id = customerId })), "data" }
        };

        var response = await httpClient.PostAsync(urlCategory, form);
        response.EnsureSuccessStatusCode();
        var json = await response.Content.ReadAsStringAsync();
        var apiResponse = JsonConvert.DeserializeObject<NhanhApiResponse<NhanhCustomerSearchData>>(json);

        if (apiResponse.Code != 1)
            return Result<NhanhSyncCustomerDto>.Failure("SYNC_NHANH_CUSTOMER_API_ERROR");

        if (apiResponse.Data?.Customers == null || !apiResponse.Data.Customers.Any())
            return Result<NhanhSyncCustomerDto>.Failure("SYNC_NHANH_CUSTOMER_NOT_FOUND");

        // Lấy customer đầu tiên từ dictionary
        var customerDto = apiResponse.Data.Customers.Values.First();
        return Result<NhanhSyncCustomerDto>.Success(customerDto);
    }
    public async Task<Result<bool>> SyncNhanhCustomerFromWebhook(object customerData, string shopId)
    {
        try
        {
            // TODO: Implement customer sync logic for NhanhVN
            return Result<bool>.Success(true);
        }
        catch (Exception ex)
        {
            return Result<bool>.Failure("SYNC_NHANH_CUSTOMER_SYNC_ERROR");
        }
    }

    public async Task<Result<bool>> DeleteNhanhProductsFromWebhook(object productIds)
    {
        try
        {
            if (productIds is List<int> nhanhProductIds)
            {
                foreach (var productId in nhanhProductIds)
                {
                    var existingItem = _syncServiceHelper.FindItemByExternalId(productId.ToString(), SyncServiceEnum.NhanhVN);
                    if (existingItem != null)
                    {
                        _itemsRepository.DeleteItems(existingItem.ItemsId);
                    }
                }
            }
            return Result<bool>.Success(true);
        }
        catch (Exception ex)
        {
            return Result<bool>.Failure("SYNC_NHANH_PRODUCT_DELETE_ERROR");
        }
    }

    public async Task<Result<bool>> DeleteNhanhOrderFromWebhook(object orderData, string shopId)
    {
        try
        {
            var nhanhOrder = JsonConvert.DeserializeObject<NhanhOrderDataDto>(orderData.ToString());
            var existingOrder = _syncServiceHelper.FindOrderByExternalId(nhanhOrder.OrderId.ToString(), SyncServiceEnum.NhanhVN);

            if (existingOrder != null)
            {
                _orderRepository.DeleteOrder(existingOrder.OrderId);
            }

            return Result<bool>.Success(true);
        }
        catch (Exception ex)
        {
            return Result<bool>.Failure("SYNC_NHANH_ORDER_DELETE_ERROR");
        }
    }

    public async Task<Result<bool>> CreateOrderToNhanh(Order order, string shopId)
    {
        var config = await _syncConfigRepository.FindByShopIdAndService(shopId, SyncServiceEnum.NhanhVN);
        if (config == null || string.IsNullOrEmpty(config.AccessToken))
            return Result<bool>.Failure("SYNC_NHANH_SHOP_NOT_CONFIGURED");

        // Giải mã SecretKey để sử dụng
        config.SecretKey = _cryptoRepository.Decrypt(config.SecretKey);

        // Tạo data object cho order
        var orderData = new
        {
            id = order.OrderNo,
            customerName = order.UserShippingAddress?.FullName,
            customerMobile = order.UserShippingAddress?.PhoneNumber,
            customerAddress = order.UserShippingAddress?.Address,
            customerCityName = order.UserShippingAddress?.ProvinceName,
            customerDistrictName = order.UserShippingAddress?.DistrictName,
            customerWardName = order.UserShippingAddress?.WardName,
            paymentMethod = order.TypePay == TypePayment.COD ? "COD" : "Online",
            customerShipFee = order.TransportPrice,
            status = order.StatusOrder == TypeOrderStatus.Pending ? "New" : order.StatusOrder == TypeOrderStatus.Verified ? "Confirmed" : "Confirming",
            description = order.Notes,
            productList = order.ListItems?.Select(x => new
            {
                id = x.ItemsId,
                name = x.ItemsName,
                code = x.ItemsCode,
                idNhanh = x.ExternalId ?? null,
                quantity = x.Quantity,
                price = x.Price
            }).ToList(),
        };

        try
        {
            using var httpClient = new HttpClient();

            // Sử dụng MultipartFormDataContent như trong curl command
            var form = new MultipartFormDataContent
            {
                { new StringContent("2.0"), "version" },
                { new StringContent(config.AppId), "appId" },
                { new StringContent(config.BusinessId.ToString()), "businessId" },
                { new StringContent(config.AccessToken), "accessToken" },
                { new StringContent(JsonConvert.SerializeObject(orderData)), "data" }
            };

            // Sử dụng đúng URL cho create order
            var response = await httpClient.PostAsync(NhanhConstants.ApiCreateOrder, form);

            if (!response.IsSuccessStatusCode)
                return Result<bool>.Failure("SYNC_NHANH_CREATE_ORDER_HTTP_ERROR");

            var json = await response.Content.ReadAsStringAsync();
            if (string.IsNullOrWhiteSpace(json))
                return Result<bool>.Failure("SYNC_NHANH_CREATE_ORDER_EMPTY_RESPONSE");

            var apiResponse = JsonConvert.DeserializeObject<NhanhApiResponse<NhanhCreateOrderResponseDto>>(json);

            if (apiResponse?.Code != 1)
                return Result<bool>.Failure("SYNC_NHANH_CREATE_ORDER_API_ERROR");

            // Lưu thông tin response vào Order entity
            if (apiResponse.Data != null)
            {
                order.ExternalSource = SyncServiceEnum.NhanhVN;
                order.ExternalId = apiResponse.Data.OrderId.ToString();
                _orderRepository.UpdateOrder(order);
            }

            return Result<bool>.Success(true);
        }
        catch (JsonException)
        {
            return Result<bool>.Failure("SYNC_NHANH_CREATE_ORDER_INVALID_JSON");
        }
        catch (HttpRequestException)
        {
            return Result<bool>.Failure("SYNC_NHANH_CREATE_ORDER_NETWORK_ERROR");
        }
        catch (Exception)
        {
            return Result<bool>.Failure("SYNC_NHANH_CREATE_ORDER_API_ERROR");
        }
    }

    public async Task<Result<List<NhanhProductCategoryDto>>> GetNhanhCategoriesAsync(string shopId)
    {
        var config = await _syncConfigRepository.FindByShopIdAndService(shopId, SyncServiceEnum.NhanhVN);
        if (config == null || string.IsNullOrEmpty(config.AccessToken))
            return Result<List<NhanhProductCategoryDto>>.Failure("SYNC_NHANH_CONFIG_NOT_FOUND");

        using var httpClient = new HttpClient();
        var urlCategory = NhanhConstants.ApiCategory;

        var form = new MultipartFormDataContent
        {
            { new StringContent("2.0"), "version" },
            { new StringContent(config.AppId), "appId" },
            { new StringContent(config.BusinessId.ToString()), "businessId" },
            { new StringContent(config.AccessToken), "accessToken" }
        };

        var response = await httpClient.PostAsync(urlCategory, form);
        response.EnsureSuccessStatusCode();
        var json = await response.Content.ReadAsStringAsync();
        var obj = JObject.Parse(json);
        if ((int)obj["code"] != 1)
            return Result<List<NhanhProductCategoryDto>>.Failure("SYNC_NHANH_CONFIG_NOT_FOUND");
        var categories = obj["data"].ToObject<List<NhanhProductCategoryDto>>();
        return Result<List<NhanhProductCategoryDto>>.Success(categories);

    }

    public NhanhProductCategoryDto FindCategoryById(List<NhanhProductCategoryDto> categories, int categoryId)
    {
        foreach (var category in categories)
        {

            if (category.Id == categoryId)
                return category;
            if (category.Childs != null && category.Childs.Count > 0)
            {
                var found = FindCategoryById(category.Childs, categoryId);
                if (found != null)
                    return found;
            }
        }
        return null;
    }

    public async Task<Category> CreateCategoryWithHierarchy(NhanhProductCategoryDto categoryDto, Shop shop, string parentCategoryId = null, int level = 1)
    {
        // Kiểm tra xem category đã tồn tại chưa
        var existingCategory = await _categoryRepository.FindByExternalId(categoryDto.Id.ToString(), SyncServiceEnum.NhanhVN);
        if (existingCategory != null)
            return existingCategory;

        // Nếu có ParentId trong Nhanh và chưa có parentCategoryId, tìm hoặc tạo parent category
        if (categoryDto.ParentId > 0 && string.IsNullOrEmpty(parentCategoryId))
        {
            var parentCategory = await _categoryRepository.FindByExternalId(categoryDto.ParentId.ToString(), SyncServiceEnum.NhanhVN);
            if (parentCategory == null)
            {
                // Tìm parent category trong danh sách categories từ Nhanh
                var categories = await GetNhanhCategoriesAsync(shop.ShopId);
                var parentCategoryDto = FindCategoryById(categories.Data, categoryDto.ParentId);
                if (parentCategoryDto != null)
                {
                    parentCategory = await CreateCategoryWithHierarchy(parentCategoryDto, shop, null, level - 1);
                }
            }
            parentCategoryId = parentCategory?.CategoryId;
        }

        // Tạo category mới
        var newCategory = new Category
        {
            CategoryId = Guid.NewGuid().ToString(),
            PartnerId = shop.PartnerId,
            CategoryName = categoryDto.Name,
            ShopId = shop.ShopId,
            ParentId = parentCategoryId,
            Image = new MediaInfo
            {
                MediaFileId = Guid.NewGuid().ToString(),
                Type = TypeMedia.IMAGE,
                Link = categoryDto.Image
            },
            CategoryType = TypeCategory.Product,
            CategoryLevel = level.ToString(),
            CategoryPosition = categoryDto.Order,
            Publish = TypeCategoryPublish.Publish,
            Active = TypeRuleActive.Actived,
            ExternalSource = SyncServiceEnum.NhanhVN,
            ExternalId = categoryDto.Id.ToString(),
            Created = DateTime.UtcNow,
            Updated = DateTime.UtcNow
        };

        var createdCategory = await _categoryRepository.CreateCategory(newCategory);

        // Tạo các child categories nếu có
        if (categoryDto.Childs != null && categoryDto.Childs.Count > 0)
        {
            foreach (var childDto in categoryDto.Childs)
            {
                await CreateCategoryWithHierarchy(childDto, shop, createdCategory.CategoryId, level + 1);
            }
        }

        return createdCategory;
    }
    public async Task<List<string>> GetAllCategoryIdsFromHierarchy(NhanhProductCategoryDto categoryObj, Shop shop)
    {
        var categoryIds = new List<string>();

        // Thêm category chính
        var mainCategory = await _syncServiceHelper.FindCategoryByExternalId(categoryObj.Id.ToString(), SyncServiceEnum.NhanhVN);
        if (mainCategory == null)
        {
            mainCategory = await CreateCategoryWithHierarchy(categoryObj, shop);
        }
        categoryIds.Add(mainCategory.CategoryId);

        // Thêm tất cả children categories nếu có
        if (categoryObj.Childs != null && categoryObj.Childs.Count > 0)
        {
            var childCategoryIds = await GetChildCategoryIds(categoryObj.Childs, shop);
            categoryIds.AddRange(childCategoryIds);
        }

        return categoryIds;
    }

    public async Task<List<string>> GetChildCategoryIds(List<NhanhProductCategoryDto> childCategories, Shop shop)
    {
        var categoryIds = new List<string>();

        foreach (var childCategory in childCategories)
        {
            var category = await _syncServiceHelper.FindCategoryByExternalId(childCategory.Id.ToString(), SyncServiceEnum.NhanhVN);
            if (category == null)
            {
                category = await CreateCategoryWithHierarchy(childCategory, shop);
            }
            categoryIds.Add(category.CategoryId);

            // Đệ quy để lấy children của children
            if (childCategory.Childs != null && childCategory.Childs.Count > 0)
            {
                var subChildCategoryIds = await GetChildCategoryIds(childCategory.Childs, shop);
                categoryIds.AddRange(subChildCategoryIds);
            }
        }

        return categoryIds;
    }

    /// <summary>
    /// Extract attributes từ format Nhanh.vn (giống logic JavaScript)
    /// </summary>
    private List<(string Name, string Value)> ExtractAttributes(List<NhanhProductAttributeDto> attributes)
    {
        var result = new List<(string Name, string Value)>();

        if (attributes == null || !attributes.Any())
            return result;

        foreach (var attr in attributes)
        {
            if (attr != null && !string.IsNullOrEmpty(attr.AttributeName) && !string.IsNullOrEmpty(attr.Name))
            {
                result.Add((attr.AttributeName, attr.Name));
            }
        }

        return result;
    }

    // public ProductDto MapNhanhProductToProductDto(NhanhProductWebhookDto nhanhProduct, string shopId, List<string> categoryIds)
    // {
    //     Shop? shop = _shopRepository.FindByShopId(shopId);
    //     if (shop == null) return null;

    //     var warehouse = _warehouseRepository.ListWarehouse(new Paging { PageSize = 1, PageIndex = 0 }, shop.PartnerId, shopId)
    // .Result?.FirstOrDefault();

    //     // Xác định IsVariant dựa trên parentId (logic từ script JavaScript)
    //     bool isVariant = false;
    //     if (nhanhProduct.ParentId > 0)
    //     {
    //         isVariant = true;
    //     }
    //     else if (nhanhProduct.ParentId == -2)
    //     {
    //         // Sản phẩm cha có biến thể - không tạo Items cho loại này (theo logic script)
    //         return null;
    //     }

    //     // Xử lý ItemsCode: nếu là biến thể thì cần lấy code của parent
    //     // TODO: Trong thực tế cần query parent product từ database hoặc cache
    //     string itemsCode = nhanhProduct.Code ?? $"NHANH_{nhanhProduct.ProductId}";

    //     // Tạo danh sách ảnh từ cả image và images
    //     var imageList = new List<MediaInfo>();

    //     // Thêm ảnh đại diện nếu có
    //     if (!string.IsNullOrEmpty(nhanhProduct.Image))
    //     {
    //         imageList.Add(new MediaInfo
    //         {
    //             MediaFileId = Guid.NewGuid().ToString(),
    //             Type = TypeMedia.IMAGE,
    //             Link = nhanhProduct.Image
    //         });
    //     }

    //     // Thêm các ảnh khác nếu có
    //     if (nhanhProduct.Images != null && nhanhProduct.Images.Any())
    //     {
    //         foreach (var img in nhanhProduct.Images)
    //         {
    //             if (!string.IsNullOrEmpty(img))
    //             {
    //                 imageList.Add(new MediaInfo
    //                 {
    //                     MediaFileId = Guid.NewGuid().ToString(),
    //                     Type = TypeMedia.IMAGE,
    //                     Link = img
    //                 });
    //             }
    //         }
    //     }

    //     // Extract attributes để xử lý variant info (giống logic JavaScript)
    //     var extractedAttributes = ExtractAttributes(nhanhProduct.Attributes);

    //     return new ProductDto
    //     {
    //         PartnerId = shop?.PartnerId,
    //         ItemsName = nhanhProduct.Name,
    //         ItemsCode = itemsCode,
    //         ExternalSource = SyncServiceEnum.NhanhVN,
    //         CategoryIds = categoryIds,
    //         Price = (long?)nhanhProduct.Price,
    //         PriceReal = (long?)nhanhProduct.Price,
    //         PriceCapital = (long?)nhanhProduct.Price,
    //         Quantity = nhanhProduct.Inventories?.Available ?? 0,
    //         ItemsInfo = nhanhProduct.Content,
    //         QuantityPurchase = null,
    //         Images = imageList,
    //         ItemsType = TypeItems.Product,
    //         ShopId = shopId,
    //         ItemsHeight = nhanhProduct.Height,
    //         ItemsWidth = nhanhProduct.Width,
    //         ItemsLength = nhanhProduct.Length,
    //         ItemsWeight = nhanhProduct.Weight,
    //         CustomTaxRate = nhanhProduct.Vat,
    //         IsTop = false,
    //         IsShow = nhanhProduct.Status == "Active",
    //         WarehouseId = warehouse?.WarehouseId,
    //         IsVariant = isVariant,
    //         Created = DateTime.TryParse(nhanhProduct.CreatedDateTime, out var created) ? created : DateTime.Now,
    //         ListVariant = isVariant && extractedAttributes.Any()
    //             ? new List<VariantBase>
    //             {
    //             new VariantBase
    //             {
    //                 // ItemsId = Guid.NewGuid().ToString(),
    //                 Price = (long?)nhanhProduct.Price,
    //                 PriceReal = (long?)nhanhProduct.Price,
    //                 PriceCapital = (long?)nhanhProduct.Price,
    //                 Quantity = nhanhProduct.Inventories?.Available ?? 0,
    //                 VariantNameOne   = extractedAttributes.Count > 0 ? extractedAttributes[0].Name : null,
    //                 VariantValueOne  = extractedAttributes.Count > 0 ? extractedAttributes[0].Value : null,
    //                 VariantNameTwo   = extractedAttributes.Count > 1 ? extractedAttributes[1].Name : null,
    //                 VariantValueTwo  = extractedAttributes.Count > 1 ? extractedAttributes[1].Value : null,
    //                 VariantNameThree = extractedAttributes.Count > 2 ? extractedAttributes[2].Name : null,
    //                 VariantValueThree= extractedAttributes.Count > 2 ? extractedAttributes[2].Value : null
    //             }
    //             }
    //             : new List<VariantBase>(),
    //     };
    // }

    /// <summary>
    /// Helper method để parse string thành long (cho price fields từ Nhanh API)
    /// </summary>
    private long? ParseNhanhPrice(string? priceString)
    {
        if (string.IsNullOrEmpty(priceString))
            return null;

        if (long.TryParse(priceString, out var result))
            return result;

        // Thử parse với decimal rồi convert sang long
        if (decimal.TryParse(priceString, out var decimalResult))
            return (long)decimalResult;

        return null;
    }

    /// <summary>
    /// Helper method để parse string thành double (cho dimension fields từ Nhanh API)
    /// </summary>
    private double? ParseNhanhDimension(string? dimensionString)
    {
        if (string.IsNullOrEmpty(dimensionString))
            return null;

        if (double.TryParse(dimensionString, out var result))
            return result;

        return null;
    }

    /// <summary>
    /// Map sản phẩm Nhanh.vn từ search API sang Items (giống logic JavaScript)
    /// </summary>
    public ProductDto MapNhanhProductToProductDto(NhanhProductDetailDto nhanhProduct, string shopId, List<string> categoryIds, Dictionary<int, NhanhProductDetailDto> parentProductsCache = null)
    {
        Shop? shop = _shopRepository.FindByShopId(shopId);
        if (shop == null) return null;

        var warehouse = _warehouseRepository.ListWarehouse(new Paging { PageSize = 1, PageIndex = 0 }, shop.PartnerId, shopId)
            .Result?.FirstOrDefault();

        // Xác định IsVariant dựa trên parentId (logic từ script JavaScript)
        bool isVariant = false;
        if (nhanhProduct.ParentId.HasValue && nhanhProduct.ParentId.Value > 0)
        {
            isVariant = true;
        }
        else if (nhanhProduct.ParentId.HasValue && nhanhProduct.ParentId.Value == -2)
        {
            // Sản phẩm cha có biến thể - không tạo Items cho loại này (theo logic script)
            return null;
        }

        // Xử lý ItemsCode: nếu là biến thể thì lấy code của parent
        string itemsCode;
        if (isVariant && nhanhProduct.ParentId.HasValue && nhanhProduct.ParentId.Value > 0 &&
            parentProductsCache?.ContainsKey(nhanhProduct.ParentId.Value) == true)
        {
            var parentProduct = parentProductsCache[nhanhProduct.ParentId.Value];
            itemsCode = parentProduct.Code ?? $"NHANH_{parentProduct.IdNhanh}";
        }
        else
        {
            itemsCode = nhanhProduct.Code ?? $"NHANH_{nhanhProduct.IdNhanh}";
        }

        // Tạo danh sách ảnh từ cả image và images
        var imageList = new List<MediaInfo>();

        // Thêm ảnh đại diện nếu có
        if (!string.IsNullOrEmpty(nhanhProduct.Image))
        {
            imageList.Add(new MediaInfo
            {
                MediaFileId = Guid.NewGuid().ToString(),
                Type = TypeMedia.IMAGE,
                Link = nhanhProduct.Image
            });
        }

        // Thêm các ảnh khác nếu có (nếu có field Images trong NhanhProductSearchDto)
        // TODO: Kiểm tra xem NhanhProductSearchDto có field Images không

        // Extract attributes để xử lý variant info
        var extractedAttributes = ExtractAttributesFromSearch(nhanhProduct.Attributes);

        return new ProductDto
        {
            PartnerId = shop?.PartnerId,
            ItemsName = nhanhProduct.Name,
            ItemsCode = itemsCode,
            ExternalSource = SyncServiceEnum.NhanhVN,
            CategoryIds = categoryIds,
            Price = ParseNhanhPrice(nhanhProduct.Price),
            PriceReal = ParseNhanhPrice(nhanhProduct.Price),
            PriceCapital = ParseNhanhPrice(nhanhProduct.ImportPrice),
            Quantity = nhanhProduct.Inventory?.Available ?? 0,
            ItemsInfo = "", // Search API không có description
            QuantityPurchase = 0,
            Images = imageList,
            ItemsType = TypeItems.Product,
            ShopId = shopId,
            ItemsHeight = ParseNhanhDimension(nhanhProduct.Height),
            ItemsWidth = ParseNhanhDimension(nhanhProduct.Width),
            ItemsLength = ParseNhanhDimension(nhanhProduct.Length),
            ItemsWeight = ParseNhanhDimension(nhanhProduct.ShippingWeight),
            CustomTaxRate = 0, // Search API không có VAT
            IsTop = nhanhProduct.ShowHot == 1,
            IsShow = nhanhProduct.Status == "Active" || nhanhProduct.Status == "New",
            WarehouseId = warehouse?.WarehouseId,
            IsVariant = isVariant,
            Created = DateTime.Now,
            ListVariant = isVariant && extractedAttributes.Any()
                ? new List<VariantBase>
                {
                    new VariantBase
                    {
                        Price = ParseNhanhPrice(nhanhProduct.Price),
                        PriceReal = ParseNhanhPrice(nhanhProduct.Price),
                        PriceCapital = ParseNhanhPrice(nhanhProduct.ImportPrice),
                        Quantity = nhanhProduct.Inventory?.Available ?? 0,
                        VariantNameOne   = extractedAttributes.Count > 0 ? extractedAttributes[0].Name : null,
                        VariantValueOne  = extractedAttributes.Count > 0 ? extractedAttributes[0].Value : null,
                        VariantNameTwo   = extractedAttributes.Count > 1 ? extractedAttributes[1].Name : null,
                        VariantValueTwo  = extractedAttributes.Count > 1 ? extractedAttributes[1].Value : null,
                        VariantNameThree = extractedAttributes.Count > 2 ? extractedAttributes[2].Name : null,
                        VariantValueThree= extractedAttributes.Count > 2 ? extractedAttributes[2].Value : null
                    }
                }
                : new List<VariantBase>(),
        };
    }

    /// <summary>
    /// Extract attributes từ search API format (giống logic JavaScript)
    /// </summary>
    private List<(string Name, string Value)> ExtractAttributesFromSearch(List<Dictionary<string, NhanhProductAttributeDetailDto>>? attributes)
    {
        var result = new List<(string Name, string Value)>();

        if (attributes == null || !attributes.Any())
            return result;

        foreach (var attrDict in attributes)
        {
            if (attrDict != null)
            {
                foreach (var attr in attrDict.Values)
                {
                    if (attr != null && !string.IsNullOrEmpty(attr.AttributeName) && !string.IsNullOrEmpty(attr.Value))
                    {
                        result.Add((attr.AttributeName, attr.Value));
                    }
                }
            }
        }

        return result;
    }

    public static TypeOrderStatus MapOrderStatusFromNhanhWebhook(string status)
    {
        if (string.IsNullOrEmpty(status))
            return TypeOrderStatus.Pending;

        return status.Trim().ToLower() switch
        {
            "new" or "confirming" or "customerconfirming" or "waitingfordelivery" or
            "waitingforpickup" or "waitingforpayment" or "waitingforstock" or
            "waitingforconfirm" or "waitingforreturn" or "waitingforrefund" => TypeOrderStatus.Pending,
            "confirmed" => TypeOrderStatus.Verified,
            "delivering" => TypeOrderStatus.Pending,
            "delivered" or "success" => TypeOrderStatus.Success,
            "canceled" => TypeOrderStatus.Failed,
            "return" => TypeOrderStatus.Refund,
            "paid" => TypeOrderStatus.Paid,
            "refund" => TypeOrderStatus.Refund,
            _ => TypeOrderStatus.Pending
        };
    }

    public static TypeTransportStatus MapTransportStatusFromNhanhWebhook(string status)
    {
        if (string.IsNullOrEmpty(status))
            return TypeTransportStatus.Created;

        return status.Trim().ToLower() switch
        {
            "new" or "confirming" or "customerconfirming" or "waitingforpayment" or
            "waitingforstock" or "waitingforconfirm" => TypeTransportStatus.Created,
            "confirmed" => TypeTransportStatus.Verified,
            "waitingfordelivery" or "waitingforpickup" => TypeTransportStatus.WaitingForDelivery,
            "delivering" => TypeTransportStatus.Delivering,
            "delivered" => TypeTransportStatus.Delivered,
            "success" => TypeTransportStatus.Success,
            "canceled" => TypeTransportStatus.Cancel,
            "return" or "waitingforreturn" => TypeTransportStatus.Refunding,
            "refund" or "waitingforrefund" => TypeTransportStatus.Refunded,
            _ => TypeTransportStatus.Created
        };
    }


}